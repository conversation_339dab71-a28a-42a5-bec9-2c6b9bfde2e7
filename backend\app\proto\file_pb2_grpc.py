# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from app.proto import file_pb2 as file__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in file_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class FileServiceStub(object):
    """文件服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetFilePaths = channel.unary_unary(
                '/file.FileService/GetFilePaths',
                request_serializer=file__pb2.FilePathsRequest.SerializeToString,
                response_deserializer=file__pb2.FilePathsResponse.FromString,
                _registered_method=True)
        self.ListDirectory = channel.unary_unary(
                '/file.FileService/ListDirectory',
                request_serializer=file__pb2.ListDirectoryRequest.SerializeToString,
                response_deserializer=file__pb2.ListDirectoryResponse.FromString,
                _registered_method=True)
        self.CheckPathExists = channel.unary_unary(
                '/file.FileService/CheckPathExists',
                request_serializer=file__pb2.PathExistsRequest.SerializeToString,
                response_deserializer=file__pb2.PathExistsResponse.FromString,
                _registered_method=True)
        self.DeleteFiles = channel.unary_unary(
                '/file.FileService/DeleteFiles',
                request_serializer=file__pb2.DeleteFilesRequest.SerializeToString,
                response_deserializer=file__pb2.DeleteFilesResponse.FromString,
                _registered_method=True)
        self.MoveFiles = channel.unary_unary(
                '/file.FileService/MoveFiles',
                request_serializer=file__pb2.MoveFilesRequest.SerializeToString,
                response_deserializer=file__pb2.MoveFilesResponse.FromString,
                _registered_method=True)
        self.CreateDirectory = channel.unary_unary(
                '/file.FileService/CreateDirectory',
                request_serializer=file__pb2.CreateDirectoryRequest.SerializeToString,
                response_deserializer=file__pb2.CreateDirectoryResponse.FromString,
                _registered_method=True)
        self.ArchivePublishedFiles = channel.unary_unary(
                '/file.FileService/ArchivePublishedFiles',
                request_serializer=file__pb2.ArchivePublishedFilesRequest.SerializeToString,
                response_deserializer=file__pb2.ArchivePublishedFilesResponse.FromString,
                _registered_method=True)
        self.CreateTripleVideo = channel.unary_unary(
                '/file.FileService/CreateTripleVideo',
                request_serializer=file__pb2.CreateTripleVideoRequest.SerializeToString,
                response_deserializer=file__pb2.CreateTripleVideoResponse.FromString,
                _registered_method=True)
        self.MergeVideos = channel.unary_unary(
                '/file.FileService/MergeVideos',
                request_serializer=file__pb2.MergeVideosRequest.SerializeToString,
                response_deserializer=file__pb2.MergeVideosResponse.FromString,
                _registered_method=True)
        self.DetectWatermark = channel.unary_unary(
                '/file.FileService/DetectWatermark',
                request_serializer=file__pb2.DetectWatermarkRequest.SerializeToString,
                response_deserializer=file__pb2.DetectWatermarkResponse.FromString,
                _registered_method=True)
        self.RemoveWatermark = channel.unary_unary(
                '/file.FileService/RemoveWatermark',
                request_serializer=file__pb2.RemoveWatermarkRequest.SerializeToString,
                response_deserializer=file__pb2.RemoveWatermarkResponse.FromString,
                _registered_method=True)
        self.BatchProcessWatermark = channel.unary_unary(
                '/file.FileService/BatchProcessWatermark',
                request_serializer=file__pb2.BatchProcessWatermarkRequest.SerializeToString,
                response_deserializer=file__pb2.BatchProcessWatermarkResponse.FromString,
                _registered_method=True)
        self.RotateVideos = channel.unary_unary(
                '/file.FileService/RotateVideos',
                request_serializer=file__pb2.RotateVideosRequest.SerializeToString,
                response_deserializer=file__pb2.RotateVideosResponse.FromString,
                _registered_method=True)
        self.GenerateVideoThumbnail = channel.unary_unary(
                '/file.FileService/GenerateVideoThumbnail',
                request_serializer=file__pb2.GenerateVideoThumbnailRequest.SerializeToString,
                response_deserializer=file__pb2.GenerateVideoThumbnailResponse.FromString,
                _registered_method=True)
        self.GetVideoPreviewInfo = channel.unary_unary(
                '/file.FileService/GetVideoPreviewInfo',
                request_serializer=file__pb2.GetVideoPreviewInfoRequest.SerializeToString,
                response_deserializer=file__pb2.GetVideoPreviewInfoResponse.FromString,
                _registered_method=True)
        self.GenerateVideoPreviewClip = channel.unary_unary(
                '/file.FileService/GenerateVideoPreviewClip',
                request_serializer=file__pb2.GenerateVideoPreviewClipRequest.SerializeToString,
                response_deserializer=file__pb2.GenerateVideoPreviewClipResponse.FromString,
                _registered_method=True)


class FileServiceServicer(object):
    """文件服务
    """

    def GetFilePaths(self, request, context):
        """获取文件路径配置
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListDirectory(self, request, context):
        """列出目录内容
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckPathExists(self, request, context):
        """检查路径是否存在
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteFiles(self, request, context):
        """删除文件
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MoveFiles(self, request, context):
        """移动文件
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDirectory(self, request, context):
        """创建文件夹
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ArchivePublishedFiles(self, request, context):
        """归档已发布文件
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateTripleVideo(self, request, context):
        """创建三拼视频
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MergeVideos(self, request, context):
        """合并视频
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DetectWatermark(self, request, context):
        """检测视频水印
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RemoveWatermark(self, request, context):
        """清除视频水印
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchProcessWatermark(self, request, context):
        """批量处理视频水印
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RotateVideos(self, request, context):
        """旋转视频
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateVideoThumbnail(self, request, context):
        """生成视频缩略图
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetVideoPreviewInfo(self, request, context):
        """获取视频预览信息
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenerateVideoPreviewClip(self, request, context):
        """生成视频预览片段
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FileServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetFilePaths': grpc.unary_unary_rpc_method_handler(
                    servicer.GetFilePaths,
                    request_deserializer=file__pb2.FilePathsRequest.FromString,
                    response_serializer=file__pb2.FilePathsResponse.SerializeToString,
            ),
            'ListDirectory': grpc.unary_unary_rpc_method_handler(
                    servicer.ListDirectory,
                    request_deserializer=file__pb2.ListDirectoryRequest.FromString,
                    response_serializer=file__pb2.ListDirectoryResponse.SerializeToString,
            ),
            'CheckPathExists': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckPathExists,
                    request_deserializer=file__pb2.PathExistsRequest.FromString,
                    response_serializer=file__pb2.PathExistsResponse.SerializeToString,
            ),
            'DeleteFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteFiles,
                    request_deserializer=file__pb2.DeleteFilesRequest.FromString,
                    response_serializer=file__pb2.DeleteFilesResponse.SerializeToString,
            ),
            'MoveFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.MoveFiles,
                    request_deserializer=file__pb2.MoveFilesRequest.FromString,
                    response_serializer=file__pb2.MoveFilesResponse.SerializeToString,
            ),
            'CreateDirectory': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDirectory,
                    request_deserializer=file__pb2.CreateDirectoryRequest.FromString,
                    response_serializer=file__pb2.CreateDirectoryResponse.SerializeToString,
            ),
            'ArchivePublishedFiles': grpc.unary_unary_rpc_method_handler(
                    servicer.ArchivePublishedFiles,
                    request_deserializer=file__pb2.ArchivePublishedFilesRequest.FromString,
                    response_serializer=file__pb2.ArchivePublishedFilesResponse.SerializeToString,
            ),
            'CreateTripleVideo': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateTripleVideo,
                    request_deserializer=file__pb2.CreateTripleVideoRequest.FromString,
                    response_serializer=file__pb2.CreateTripleVideoResponse.SerializeToString,
            ),
            'MergeVideos': grpc.unary_unary_rpc_method_handler(
                    servicer.MergeVideos,
                    request_deserializer=file__pb2.MergeVideosRequest.FromString,
                    response_serializer=file__pb2.MergeVideosResponse.SerializeToString,
            ),
            'DetectWatermark': grpc.unary_unary_rpc_method_handler(
                    servicer.DetectWatermark,
                    request_deserializer=file__pb2.DetectWatermarkRequest.FromString,
                    response_serializer=file__pb2.DetectWatermarkResponse.SerializeToString,
            ),
            'RemoveWatermark': grpc.unary_unary_rpc_method_handler(
                    servicer.RemoveWatermark,
                    request_deserializer=file__pb2.RemoveWatermarkRequest.FromString,
                    response_serializer=file__pb2.RemoveWatermarkResponse.SerializeToString,
            ),
            'BatchProcessWatermark': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchProcessWatermark,
                    request_deserializer=file__pb2.BatchProcessWatermarkRequest.FromString,
                    response_serializer=file__pb2.BatchProcessWatermarkResponse.SerializeToString,
            ),
            'RotateVideos': grpc.unary_unary_rpc_method_handler(
                    servicer.RotateVideos,
                    request_deserializer=file__pb2.RotateVideosRequest.FromString,
                    response_serializer=file__pb2.RotateVideosResponse.SerializeToString,
            ),
            'GenerateVideoThumbnail': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateVideoThumbnail,
                    request_deserializer=file__pb2.GenerateVideoThumbnailRequest.FromString,
                    response_serializer=file__pb2.GenerateVideoThumbnailResponse.SerializeToString,
            ),
            'GetVideoPreviewInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetVideoPreviewInfo,
                    request_deserializer=file__pb2.GetVideoPreviewInfoRequest.FromString,
                    response_serializer=file__pb2.GetVideoPreviewInfoResponse.SerializeToString,
            ),
            'GenerateVideoPreviewClip': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateVideoPreviewClip,
                    request_deserializer=file__pb2.GenerateVideoPreviewClipRequest.FromString,
                    response_serializer=file__pb2.GenerateVideoPreviewClipResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'file.FileService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('file.FileService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class FileService(object):
    """文件服务
    """

    @staticmethod
    def GetFilePaths(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/GetFilePaths',
            file__pb2.FilePathsRequest.SerializeToString,
            file__pb2.FilePathsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListDirectory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/ListDirectory',
            file__pb2.ListDirectoryRequest.SerializeToString,
            file__pb2.ListDirectoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CheckPathExists(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/CheckPathExists',
            file__pb2.PathExistsRequest.SerializeToString,
            file__pb2.PathExistsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/DeleteFiles',
            file__pb2.DeleteFilesRequest.SerializeToString,
            file__pb2.DeleteFilesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MoveFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/MoveFiles',
            file__pb2.MoveFilesRequest.SerializeToString,
            file__pb2.MoveFilesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDirectory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/CreateDirectory',
            file__pb2.CreateDirectoryRequest.SerializeToString,
            file__pb2.CreateDirectoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ArchivePublishedFiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/ArchivePublishedFiles',
            file__pb2.ArchivePublishedFilesRequest.SerializeToString,
            file__pb2.ArchivePublishedFilesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateTripleVideo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/CreateTripleVideo',
            file__pb2.CreateTripleVideoRequest.SerializeToString,
            file__pb2.CreateTripleVideoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MergeVideos(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/MergeVideos',
            file__pb2.MergeVideosRequest.SerializeToString,
            file__pb2.MergeVideosResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DetectWatermark(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/DetectWatermark',
            file__pb2.DetectWatermarkRequest.SerializeToString,
            file__pb2.DetectWatermarkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RemoveWatermark(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/RemoveWatermark',
            file__pb2.RemoveWatermarkRequest.SerializeToString,
            file__pb2.RemoveWatermarkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchProcessWatermark(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/BatchProcessWatermark',
            file__pb2.BatchProcessWatermarkRequest.SerializeToString,
            file__pb2.BatchProcessWatermarkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RotateVideos(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/RotateVideos',
            file__pb2.RotateVideosRequest.SerializeToString,
            file__pb2.RotateVideosResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenerateVideoThumbnail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/GenerateVideoThumbnail',
            file__pb2.GenerateVideoThumbnailRequest.SerializeToString,
            file__pb2.GenerateVideoThumbnailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetVideoPreviewInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/GetVideoPreviewInfo',
            file__pb2.GetVideoPreviewInfoRequest.SerializeToString,
            file__pb2.GetVideoPreviewInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenerateVideoPreviewClip(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/file.FileService/GenerateVideoPreviewClip',
            file__pb2.GenerateVideoPreviewClipRequest.SerializeToString,
            file__pb2.GenerateVideoPreviewClipResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
