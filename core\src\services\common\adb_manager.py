"""
ADB 连接管理器
监控和恢复 ADB 连接，防止设备连接丢失导致的崩溃
"""

import asyncio
import logging
import subprocess
import time
from typing import Optional, List

logger = logging.getLogger(__name__)

class ADBManager:
    """ADB 连接管理器"""
    
    def __init__(self):
        self.last_check_time = 0
        self.check_interval = 30  # 30秒检查一次
        self.adb_path = "adb"  # 默认使用系统PATH中的adb
        
    async def check_device_connection(self, device_id: str) -> bool:
        """检查设备连接状态"""
        try:
            # 执行 adb devices 命令
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "devices",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                logger.error(f"ADB命令执行失败: {stderr.decode()}")
                return False
            
            # 解析输出，查找设备
            output = stdout.decode()
            lines = output.strip().split('\n')[1:]  # 跳过标题行
            
            for line in lines:
                if device_id in line and 'device' in line:
                    logger.debug(f"设备 {device_id} 连接正常")
                    return True
            
            logger.warning(f"设备 {device_id} 未找到或状态异常")
            return False
            
        except Exception as e:
            logger.error(f"检查设备连接失败: {str(e)}")
            return False
    
    async def get_connected_devices(self) -> List[str]:
        """获取所有连接的设备列表"""
        try:
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "devices",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                return []
            
            output = stdout.decode()
            lines = output.strip().split('\n')[1:]  # 跳过标题行
            
            devices = []
            for line in lines:
                if '\tdevice' in line:
                    device_id = line.split('\t')[0]
                    devices.append(device_id)
            
            return devices
            
        except Exception as e:
            logger.error(f"获取设备列表失败: {str(e)}")
            return []
    
    async def restart_adb_server(self) -> bool:
        """重启 ADB 服务器"""
        try:
            logger.info("🔧 重启 ADB 服务器...")
            
            # 停止 ADB 服务器
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "kill-server",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            
            # 等待一下
            await asyncio.sleep(2)
            
            # 启动 ADB 服务器
            process = await asyncio.create_subprocess_exec(
                self.adb_path, "start-server",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("✅ ADB 服务器重启成功")
                return True
            else:
                logger.error(f"❌ ADB 服务器重启失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"重启 ADB 服务器异常: {str(e)}")
            return False
    
    async def reconnect_device(self, device_id: str) -> bool:
        """重新连接设备"""
        try:
            logger.info(f"🔧 尝试重新连接设备: {device_id}")
            
            # 如果是网络设备，尝试重新连接
            if ':' in device_id:  # 网络ADB (IP:PORT)
                # 先断开
                process = await asyncio.create_subprocess_exec(
                    self.adb_path, "disconnect", device_id,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await process.communicate()
                
                await asyncio.sleep(1)
                
                # 重新连接
                process = await asyncio.create_subprocess_exec(
                    self.adb_path, "connect", device_id,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await process.communicate()
                
                if process.returncode == 0:
                    logger.info(f"✅ 设备 {device_id} 重新连接成功")
                    return True
                else:
                    logger.error(f"❌ 设备 {device_id} 重新连接失败: {stderr.decode()}")
                    return False
            else:
                # USB设备，重启ADB服务器
                return await self.restart_adb_server()
                
        except Exception as e:
            logger.error(f"重新连接设备异常: {str(e)}")
            return False
    
    async def ensure_device_connected(self, device_id: str) -> bool:
        """确保设备连接（检查并恢复）"""
        # 检查是否需要检查（避免过于频繁）
        current_time = time.time()
        if current_time - self.last_check_time < self.check_interval:
            return True  # 假设连接正常
        
        self.last_check_time = current_time
        
        # 检查连接状态
        if await self.check_device_connection(device_id):
            return True
        
        logger.warning(f"⚠️ 设备 {device_id} 连接丢失，尝试恢复...")
        
        # 尝试恢复连接
        recovery_success = await self.reconnect_device(device_id)
        
        if recovery_success:
            # 再次检查
            await asyncio.sleep(3)
            return await self.check_device_connection(device_id)
        
        return False

# 全局 ADB 管理器
adb_manager = ADBManager()

def get_adb_manager() -> ADBManager:
    """获取全局 ADB 管理器"""
    return adb_manager
