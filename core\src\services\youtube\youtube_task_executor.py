"""
YouTube任务执行器
负责执行YouTube上传任务
"""

import os
import logging
import asyncio
import datetime
import json
import time
from typing import Dict, List, Any, Optional

from .youtube_uploader_v3 import YouTubeUploaderV3 as YouTubeUploader

logger = logging.getLogger(__name__)

class YouTubeTaskExecutor:
    """YouTube任务执行器类"""

    def __init__(self, main_service):
        """初始化YouTube任务执行器

        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        self.uploaders = {}  # 设备ID -> YouTubeUploader

    async def execute_youtube_upload_task(self, task_id: str, task: Dict[str, Any], add_task_log, publish_task_status) -> bool:
        """执行YouTube上传任务

        Args:
            task_id: 任务ID
            task: 任务数据
            add_task_log: 添加任务日志的函数
            publish_task_status: 发布任务状态的函数

        Returns:
            bool: 是否成功
        """
        try:
            # 获取设备ID
            device_id = task.get("device_id")
            if not device_id:
                add_task_log(task_id, "任务未指定设备ID", "error")
                return False

            # 获取内容路径
            content_path = task.get("content_path")
            if not content_path:
                add_task_log(task_id, "任务未指定内容路径", "error")
                return False

            # 获取账号ID
            account_id = task.get("account_id")
            if not account_id:
                add_task_log(task_id, "任务未指定账号ID", "error")
                return False

            # 获取平台ID
            platform_id = task.get("platform_id")
            if not platform_id:
                add_task_log(task_id, "任务未指定平台ID", "error")
                return False

            # 获取完整的任务数据用于调试
            logger.info(f"=== 任务数据调试开始 ===")
            logger.info(f"完整任务数据: {task}")
            logger.info(f"任务数据类型: {type(task)}")
            logger.info(f"任务数据键: {list(task.keys()) if isinstance(task, dict) else 'Not a dict'}")

            # 获取视频元数据
            metadata = task.get("metadata", {})
            logger.info(f"直接获取的metadata: {metadata}")
            logger.info(f"metadata类型: {type(metadata)}")

            # 检查是否有params字段
            if "params" in task:
                params = task.get("params", {})
                logger.info(f"发现params字段: {params}")
                logger.info(f"params类型: {type(params)}")
                logger.info(f"params键: {list(params.keys()) if isinstance(params, dict) else 'Not a dict'}")

                # 检查params中是否有metadata相关的键
                metadata_keys = [k for k in params.keys() if 'metadata' in k.lower()]
                logger.info(f"params中包含metadata的键: {metadata_keys}")
            else:
                logger.warning("任务数据中没有params字段")

            # 如果metadata为空，尝试从params中提取metadata信息
            if not metadata and "params" in task:
                params = task.get("params", {})
                logger.info(f"从params中提取元数据: {params}")

                # 提取以metadata_开头的参数
                metadata = {}
                for key, value in params.items():
                    if key.startswith("metadata_"):
                        # 移除metadata_前缀
                        metadata_key = key[9:]  # 去掉"metadata_"前缀

                        # 如果是selectedMusic，需要从JSON字符串解析
                        if metadata_key == "selectedMusic":
                            try:
                                import json
                                parsed_value = json.loads(value)
                                metadata[metadata_key] = parsed_value
                                logger.info(f"解析JSON元数据: {metadata_key} = {parsed_value}")
                            except (json.JSONDecodeError, TypeError) as e:
                                logger.warning(f"解析selectedMusic JSON失败: {e}, 原始值: {value}")
                                metadata[metadata_key] = value
                        else:
                            metadata[metadata_key] = value
                            logger.info(f"提取元数据: {metadata_key} = {value}")

                logger.info(f"从params提取的完整metadata: {metadata}")

            logger.info(f"最终使用的任务元数据: {metadata}")

            # 提取各个字段并记录详细日志
            title_template = metadata.get("title_template") or metadata.get("titleTemplate", "视频 {index}")
            logger.info(f"标题模板: {title_template}")

            description = metadata.get("description", "")
            logger.info(f"描述: {description}")

            privacy = metadata.get("privacy_status") or metadata.get("privacyStatus", "public")
            logger.info(f"隐私设置: {privacy}")

            # 支持多种字段名：contentType（前端）、content_type（后端）
            content_type_from_content_type = metadata.get("content_type")
            content_type_from_contentType = metadata.get("contentType")
            logger.info(f"从content_type字段获取: {content_type_from_content_type}")
            logger.info(f"从contentType字段获取: {content_type_from_contentType}")

            content_type = content_type_from_content_type or content_type_from_contentType or "video"
            logger.info(f"最终确定的内容类型: {content_type}")

            # 获取选中的音乐
            selected_music = metadata.get("selectedMusic", [])
            logger.info(f"选中的音乐: {selected_music}")
            logger.info(f"选中音乐数量: {len(selected_music) if selected_music else 0}")

            logger.info(f"=== 任务数据调试结束 ===")

            # 获取视频文件列表
            video_files = self._get_video_files(content_path)
            if not video_files:
                add_task_log(task_id, f"在路径 {content_path} 中未找到视频文件", "error")
                return False

            # 更新任务数据
            task["total_videos"] = len(video_files)
            task["processed_videos"] = 0
            task["successful_videos"] = 0
            task["failed_videos"] = 0
            task["current_video"] = ""
            task["video_progress"] = 0

            # 创建YouTube上传器实例
            youtube_uploader_instance = await self._get_uploader(device_id)
            if not youtube_uploader_instance:
                add_task_log(task_id, f"创建YouTube上传器失败，设备ID: {device_id}", "error")
                return False

            # 连接到设备
            add_task_log(task_id, f"正在连接到设备 {device_id}", "info")
            connect_success = await youtube_uploader_instance.connect()
            if not connect_success:
                add_task_log(task_id, f"连接设备 {device_id} 失败", "error")
                return False

            try:
                # 启动YouTube应用（包含网络连接检查和V2rayN启动）
                add_task_log(task_id, "正在启动YouTube应用（包含网络连接检查和V2rayN启动）...", "info")
                launch_success = await youtube_uploader_instance.launch_app()
                if not launch_success:
                    add_task_log(task_id, "启动YouTube应用失败", "error")
                    return False

                # 处理每个视频文件
                for index, video_file in enumerate(video_files):
                    # 检查任务是否被取消
                    if task.get("status") == "canceled" or task.get("cancel_requested"):
                        add_task_log(task_id, "任务已取消", "warning")
                        task["status"] = "canceled"
                        task["end_time"] = datetime.datetime.now().isoformat()
                        await publish_task_status(task_id)
                        break



                    # 更新当前处理的视频
                    video_name = os.path.basename(video_file)
                    task["current_video"] = video_name
                    task["video_progress"] = 0
                    task["progress"] = int((index / len(video_files)) * 100)
                    await publish_task_status(task_id)

                    # 添加日志
                    add_task_log(task_id, f"开始处理视频 {index+1}/{len(video_files)}: {video_name}", "info")

                    # 检查V2rayN是否运行并且设备已联网
                    add_task_log(task_id, "检查网络连接状态...", "info")

                    # 准备视频标题和描述
                    # 支持多种模板变量：{index}, {filename}, {name}
                    filename_without_ext = os.path.splitext(video_name)[0]
                    try:
                        video_title = title_template.format(
                            index=index+1,
                            filename=filename_without_ext,  # 支持{filename}
                            name=filename_without_ext       # 支持{name}
                        )
                    except KeyError as e:
                        logger.warning(f"标题模板中包含未支持的变量: {e}")
                        # 如果模板格式错误，使用默认格式
                        video_title = f"{filename_without_ext} - 视频 {index+1}"
                        logger.info(f"使用默认标题格式: {video_title}")

                    add_task_log(task_id, f"视频标题: {video_title}", "info")

                    # 🔧 设置进度回调，让上传器能够直接更新任务进度
                    def progress_callback(progress, message):
                        """进度更新回调"""
                        try:
                            # 更新任务进度（35-95%范围，为工作流执行预留空间）
                            workflow_progress = 35 + int((progress / 100) * 60)  # 35% + 60% = 95%
                            task["progress"] = workflow_progress
                            task["video_progress"] = progress

                            # 异步发布状态更新
                            asyncio.create_task(publish_task_status(task_id))

                            add_task_log(task_id, f"上传进度: {progress}% - {message}", "info")
                            logger.info(f"📊 任务{task_id}进度更新: {workflow_progress}% (视频进度: {progress}%)")
                        except Exception as e:
                            logger.error(f"进度回调异常: {str(e)}")

                    # 设置进度回调
                    youtube_uploader_instance.set_progress_callback(progress_callback)

                    # 创建上传状态监控任务（作为备用）
                    upload_monitor_task = asyncio.create_task(
                        self._monitor_upload_progress(youtube_uploader_instance, task, task_id, publish_task_status)
                    )

                    # 使用新的一体化上传方法
                    add_task_log(task_id, "开始执行上传任务...", "info")
                    logger.info(f"调用execute_upload_task方法，视频路径: {video_file}, 标题: {video_title}")
                    logger.info(f"🔧 传递metadata到上传器: {metadata}")

                    # 特别检查音频设置
                    if metadata:
                        audio_settings = {
                            'keepOriginalAudio': metadata.get('keepOriginalAudio', '未设置'),
                            'originalAudioPercentage': metadata.get('originalAudioPercentage', '未设置'),
                            'musicVolumePercentage': metadata.get('musicVolumePercentage', '未设置')
                        }
                        logger.info(f"🔊 音频设置传递检查: {audio_settings}")
                    else:
                        logger.warning("⚠️ metadata为空，音频设置将使用默认值")

                    try:
                        upload_success = await youtube_uploader_instance.execute_upload_task(
                            video_path=video_file,
                            title=video_title,
                            description=description,
                            privacy=privacy,
                            content_type=content_type,
                            selected_music=selected_music,  # 传递选中的音乐
                            metadata=metadata  # 传递完整的元数据（包含音频设置）
                        )
                        logger.info(f"execute_upload_task方法执行完成，结果: {upload_success}")
                    except Exception as upload_error:
                        logger.error(f"执行上传任务异常: {str(upload_error)}", exc_info=True)
                        add_task_log(task_id, f"执行上传任务异常: {str(upload_error)}", "error")
                        upload_success = False

                    # 取消监控任务
                    upload_monitor_task.cancel()

                    if upload_success:
                        add_task_log(task_id, f"视频 {video_name} 上传成功", "success")
                        task["successful_videos"] += 1
                    else:
                        # 获取上传状态信息
                        upload_status = await youtube_uploader_instance.get_upload_status()
                        error_message = upload_status.get("message", "未知错误")

                        # 检查是否是网络连接问题
                        if "网络连接检查失败" in error_message:
                            add_task_log(task_id, f"视频 {video_name} 上传失败: 网络连接检查失败", "error")
                            add_task_log(task_id, "请确保V2rayN已启动并且已连接到服务器", "error")
                            add_task_log(task_id, "建议手动打开手机上的V2rayN应用，点击服务器连接并确认连接状态", "error")
                        else:
                            add_task_log(task_id, f"视频 {video_name} 上传失败: {error_message}", "error")

                        task["failed_videos"] += 1

                    # 清理当前上传的视频文件
                    try:
                        logger.info(f"🧹 清理视频文件: {video_name}")
                        cleanup_success = await youtube_uploader_instance.cleanup_uploaded_file()
                        if cleanup_success:
                            add_task_log(task_id, f"✅ 已清理视频文件: {video_name}", "info")
                        else:
                            add_task_log(task_id, f"⚠️ 清理视频文件失败: {video_name}", "warning")
                    except Exception as cleanup_error:
                        logger.error(f"清理视频文件异常: {str(cleanup_error)}")
                        add_task_log(task_id, f"清理视频文件异常: {str(cleanup_error)}", "warning")

                    # 更新处理进度
                    task["processed_videos"] += 1
                    task["progress"] = int(((index + 1) / len(video_files)) * 100)
                    await publish_task_status(task_id)

                    # 等待一段时间再处理下一个视频
                    await asyncio.sleep(5)

                # 任务完成
                if task.get("status") != "canceled":
                    # 只有在所有视频都成功上传时，才将状态设置为"completed"
                    if task["failed_videos"] == 0 and task["successful_videos"] > 0:
                        task["status"] = "completed"
                        task["progress"] = 100
                        add_task_log(task_id, f"任务执行成功，所有视频上传完成: {task['successful_videos']}/{task['total_videos']}", "success")
                    else:
                        # 如果有失败的视频，将状态设置为"partial_completed"，这样前端不会自动跳转
                        task["status"] = "partial_completed"
                        task["progress"] = 100
                        add_task_log(task_id, f"任务执行部分完成，成功: {task['successful_videos']}，失败: {task['failed_videos']}", "warning")

                # 返回执行结果
                return task["failed_videos"] == 0

            finally:
                # 最终清理检查（防止有遗漏的文件）
                try:
                    logger.info("🧹 执行最终清理检查...")
                    cleanup_success = await youtube_uploader_instance.cleanup_uploaded_file()
                    if cleanup_success:
                        add_task_log(task_id, "✅ 最终清理检查完成", "info")
                except Exception as cleanup_error:
                    logger.error(f"最终清理检查异常: {str(cleanup_error)}")
                    add_task_log(task_id, f"最终清理检查异常: {str(cleanup_error)}", "warning")

                # 断开连接
                await youtube_uploader_instance.disconnect()
                add_task_log(task_id, "已断开设备连接", "info")

        except Exception as e:
            logger.error(f"执行YouTube上传任务异常: {str(e)}", exc_info=True)
            add_task_log(task_id, f"执行任务异常: {str(e)}", "error")
            return False

    async def _monitor_upload_progress(self, youtube_uploader_instance, task, task_id, publish_task_status):
        """监控上传进度

        Args:
            youtube_uploader_instance: YouTube上传器实例
            task: 任务数据
            task_id: 任务ID
            publish_task_status: 发布任务状态的函数
        """
        try:
            while True:
                # 获取上传状态
                upload_status = await youtube_uploader_instance.get_upload_status()

                # 更新任务数据
                task["video_progress"] = upload_status["progress"]

                # 更新设备使用情况
                task["device_usage"] = {
                    "cpu": 60,  # 模拟CPU使用率
                    "memory": 70,  # 模拟内存使用率
                    "network": "已连接"
                }

                # 发布任务状态
                await publish_task_status(task_id)

                # 等待一段时间再检查
                await asyncio.sleep(3)

        except asyncio.CancelledError:
            # 任务被取消，正常退出
            pass
        except Exception as e:
            logger.error(f"监控上传进度异常: {str(e)}", exc_info=True)

    def _get_video_files(self, content_path: str) -> List[str]:
        """获取视频文件列表

        Args:
            content_path: 内容路径

        Returns:
            List[str]: 视频文件路径列表
        """
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.webm']
        video_files = []

        try:
            logger.info(f"🔍 检查内容路径: {content_path}")

            # 🔧 修复：使用更强健的路径检查方法
            path_exists = False
            is_directory = False
            is_file = False

            try:
                # 尝试多种方式检查路径
                path_exists = os.path.exists(content_path)
                if path_exists:
                    is_directory = os.path.isdir(content_path)
                    is_file = os.path.isfile(content_path)
                    logger.info(f"✅ 路径存在检查: exists={path_exists}, isdir={is_directory}, isfile={is_file}")
                else:
                    # 如果直接检查失败，尝试通过父目录检查
                    parent_dir = os.path.dirname(content_path)
                    filename = os.path.basename(content_path)
                    logger.info(f"🔍 尝试通过父目录检查: parent={parent_dir}, filename={filename}")

                    if os.path.exists(parent_dir) and os.path.isdir(parent_dir):
                        # 列出父目录中的文件，查找匹配的文件
                        for file in os.listdir(parent_dir):
                            if file == filename:
                                path_exists = True
                                is_file = True
                                logger.info(f"✅ 通过父目录找到文件: {file}")
                                break

                        if not path_exists:
                            logger.warning(f"⚠️ 在父目录中未找到文件: {filename}")
                            # 列出父目录中的所有文件用于调试
                            files_in_dir = os.listdir(parent_dir)
                            logger.info(f"父目录中的文件: {files_in_dir}")
                    else:
                        logger.error(f"❌ 父目录不存在或不是目录: {parent_dir}")

            except Exception as check_error:
                logger.error(f"路径检查异常: {str(check_error)}")
                return []

            if not path_exists:
                logger.error(f"❌ 内容路径不存在: {content_path}")
                return []

            # 如果是目录，获取所有视频文件
            if is_directory:
                logger.info(f"📁 处理目录: {content_path}")
                for file in os.listdir(content_path):
                    file_path = os.path.join(content_path, file)
                    if os.path.isfile(file_path) and any(file.lower().endswith(ext) for ext in video_extensions):
                        video_files.append(file_path)
                        logger.info(f"✅ 找到视频文件: {file}")
            # 如果是文件，检查是否是视频文件
            elif is_file and any(content_path.lower().endswith(ext) for ext in video_extensions):
                logger.info(f"📄 处理单个文件: {content_path}")
                video_files.append(content_path)
                logger.info(f"✅ 确认为视频文件")

            # 按文件名排序
            video_files.sort()

            logger.info(f"🎬 在路径 {content_path} 中找到 {len(video_files)} 个视频文件")
            if video_files:
                for i, video_file in enumerate(video_files):
                    logger.info(f"  {i+1}. {os.path.basename(video_file)}")

            return video_files

        except Exception as e:
            logger.error(f"获取视频文件列表异常: {str(e)}", exc_info=True)
            return []

    async def _get_uploader(self, device_id: str) -> Optional[YouTubeUploader]:
        """获取YouTube上传器

        Args:
            device_id: 设备ID

        Returns:
            Optional[YouTubeUploader]: YouTube上传器
        """
        try:
            # 检查是否已存在上传器
            if device_id in self.uploaders:
                return self.uploaders[device_id]

            # 创建新的上传器
            uploader = YouTubeUploader(device_id)
            self.uploaders[device_id] = uploader

            return uploader

        except Exception as e:
            logger.error(f"获取YouTube上传器异常: {str(e)}", exc_info=True)
            return None
