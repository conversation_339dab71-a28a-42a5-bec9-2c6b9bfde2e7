"""
UiAutomator2 崩溃分析器
分析崩溃模式，找出根本原因
"""

import logging
import time
import json
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class CrashEvent:
    """崩溃事件记录"""
    timestamp: str
    step_name: str
    element_name: str
    task_progress: int
    task_runtime: float  # 任务运行时间（秒）
    error_message: str
    memory_usage: float = 0.0  # MB
    page_elements_count: int = 0
    current_activity: str = ""

class CrashAnalyzer:
    """崩溃分析器"""
    
    def __init__(self):
        self.crash_events: List[CrashEvent] = []
        self.task_start_times: Dict[str, float] = {}
        
    def record_task_start(self, task_id: str):
        """记录任务开始时间"""
        self.task_start_times[task_id] = time.time()
        
    def record_crash(self, 
                    task_id: str,
                    step_name: str, 
                    element_name: str, 
                    task_progress: int,
                    error_message: str,
                    memory_usage: float = 0.0,
                    page_elements_count: int = 0,
                    current_activity: str = ""):
        """记录崩溃事件"""
        
        # 计算任务运行时间
        task_runtime = 0.0
        if task_id in self.task_start_times:
            task_runtime = time.time() - self.task_start_times[task_id]
        
        crash_event = CrashEvent(
            timestamp=datetime.now().isoformat(),
            step_name=step_name,
            element_name=element_name,
            task_progress=task_progress,
            task_runtime=task_runtime,
            error_message=error_message,
            memory_usage=memory_usage,
            page_elements_count=page_elements_count,
            current_activity=current_activity
        )
        
        self.crash_events.append(crash_event)
        logger.error(f"🚨 记录崩溃事件: {step_name} -> {element_name} (进度: {task_progress}%, 运行时间: {task_runtime:.1f}s)")
        
        # 分析崩溃模式
        self._analyze_crash_pattern()
        
    def _analyze_crash_pattern(self):
        """分析崩溃模式"""
        if len(self.crash_events) < 2:
            return
            
        recent_crashes = self.crash_events[-5:]  # 最近5次崩溃
        
        # 分析崩溃步骤
        crash_steps = [event.step_name for event in recent_crashes]
        crash_elements = [event.element_name for event in recent_crashes]
        crash_runtimes = [event.task_runtime for event in recent_crashes]
        crash_progress = [event.task_progress for event in recent_crashes]
        
        logger.warning("📊 崩溃模式分析:")
        logger.warning(f"   - 最近崩溃步骤: {crash_steps}")
        logger.warning(f"   - 最近崩溃元素: {crash_elements}")
        logger.warning(f"   - 平均运行时间: {sum(crash_runtimes)/len(crash_runtimes):.1f}秒")
        logger.warning(f"   - 平均崩溃进度: {sum(crash_progress)/len(crash_progress):.1f}%")
        
        # 检查是否有明显的模式
        if len(set(crash_elements)) == 1:
            logger.error(f"🚨 发现崩溃模式: 元素 '{crash_elements[0]}' 连续崩溃!")
            
        if all(runtime > 300 for runtime in crash_runtimes):  # 5分钟
            logger.error("🚨 发现崩溃模式: 所有崩溃都发生在运行5分钟后!")
            
        if all(progress > 40 for progress in crash_progress):
            logger.error("🚨 发现崩溃模式: 所有崩溃都发生在40%进度后!")
    
    def get_crash_statistics(self) -> Dict[str, Any]:
        """获取崩溃统计"""
        if not self.crash_events:
            return {"total_crashes": 0}
            
        # 按元素统计
        element_crashes = {}
        step_crashes = {}
        runtime_crashes = []
        
        for event in self.crash_events:
            element_crashes[event.element_name] = element_crashes.get(event.element_name, 0) + 1
            step_crashes[event.step_name] = step_crashes.get(event.step_name, 0) + 1
            runtime_crashes.append(event.task_runtime)
        
        return {
            "total_crashes": len(self.crash_events),
            "most_problematic_element": max(element_crashes.items(), key=lambda x: x[1]) if element_crashes else None,
            "most_problematic_step": max(step_crashes.items(), key=lambda x: x[1]) if step_crashes else None,
            "average_crash_runtime": sum(runtime_crashes) / len(runtime_crashes),
            "element_crash_counts": element_crashes,
            "step_crash_counts": step_crashes
        }
    
    def export_crash_data(self, filename: str = "crash_analysis.json"):
        """导出崩溃数据"""
        try:
            data = {
                "crash_events": [asdict(event) for event in self.crash_events],
                "statistics": self.get_crash_statistics()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"📊 崩溃数据已导出到: {filename}")
            
        except Exception as e:
            logger.error(f"❌ 导出崩溃数据失败: {str(e)}")

# 全局崩溃分析器
crash_analyzer = CrashAnalyzer()

def get_crash_analyzer() -> CrashAnalyzer:
    """获取全局崩溃分析器"""
    return crash_analyzer
