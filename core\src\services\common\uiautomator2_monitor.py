"""
UiAutomator2 健康监控器
监控 UiAutomator2 的健康状态，预防崩溃
"""

import asyncio
import logging
import time
from typing import Optional

logger = logging.getLogger(__name__)

class UiAutomator2Monitor:
    """UiAutomator2 健康监控器"""
    
    def __init__(self):
        self.last_successful_operation = time.time()
        self.consecutive_failures = 0
        self.max_consecutive_failures = 5
        self.operation_count = 0
        self.failure_rate_threshold = 0.5  # 50%失败率阈值
        
    def record_operation_success(self):
        """记录操作成功"""
        self.last_successful_operation = time.time()
        self.consecutive_failures = 0
        self.operation_count += 1
        
    def record_operation_failure(self):
        """记录操作失败"""
        self.consecutive_failures += 1
        self.operation_count += 1
        
    def is_healthy(self) -> bool:
        """检查是否健康"""
        # 检查连续失败次数
        if self.consecutive_failures >= self.max_consecutive_failures:
            logger.warning(f"UiAutomator2不健康：连续失败 {self.consecutive_failures} 次")
            return False
            
        # 检查最后成功操作时间
        time_since_success = time.time() - self.last_successful_operation
        if time_since_success > 300:  # 5分钟没有成功操作
            logger.warning(f"UiAutomator2不健康：{time_since_success:.1f}秒没有成功操作")
            return False
            
        return True
        
    def should_pause_operations(self) -> bool:
        """是否应该暂停操作"""
        return not self.is_healthy()
        
    def get_health_status(self) -> dict:
        """获取健康状态"""
        return {
            "consecutive_failures": self.consecutive_failures,
            "operation_count": self.operation_count,
            "time_since_success": time.time() - self.last_successful_operation,
            "is_healthy": self.is_healthy()
        }
        
    async def wait_for_recovery(self, max_wait_time: int = 60):
        """等待恢复"""
        logger.info(f"等待UiAutomator2恢复，最长等待 {max_wait_time} 秒")
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            if self.is_healthy():
                logger.info("UiAutomator2已恢复健康")
                return True
            await asyncio.sleep(5)
            
        logger.warning("UiAutomator2恢复超时")
        return False

# 全局监控器实例
uiautomator2_monitor = UiAutomator2Monitor()

def get_monitor() -> UiAutomator2Monitor:
    """获取全局监控器"""
    return uiautomator2_monitor

async def safe_element_operation(operation_func, monitor: Optional[UiAutomator2Monitor] = None):
    """安全的元素操作包装器"""
    if monitor is None:
        monitor = get_monitor()
        
    # 检查是否应该暂停操作
    if monitor.should_pause_operations():
        logger.warning("UiAutomator2不健康，暂停操作")
        recovery_success = await monitor.wait_for_recovery()
        if not recovery_success:
            raise Exception("UiAutomator2恢复失败")
    
    try:
        result = await operation_func()
        monitor.record_operation_success()
        return result
    except Exception as e:
        monitor.record_operation_failure()
        logger.warning(f"元素操作失败: {str(e)}")
        raise e
